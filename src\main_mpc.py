import time
from multiprocessing import Pipe, Process, Queue
from multiprocessing.connection import Connection

from mpc import MpcService, MQTTService, StateService


def run_mqtt_service(output_queues: list[Queue]):
    mqtt_service = MQTTService(output_queues)
    mqtt_service.start()


def run_state_service(input_queue: Queue, state_conn: Connection):
    state_service = StateService(input_queue, state_conn)
    state_service.start()


def run_mpc_service(mpc_conn: Connection):
    mpc_service = MpcService(mpc_conn)
    mpc_service.start()


def mpc_start():
    inst_queue = Queue()  # 仪表数据队列
    state_conn, mpc_conn = Pipe()  # MPC与State通讯管道

    state_service_process = Process(target=run_state_service, args=(inst_queue, state_conn))
    state_service_process.start()

    mpc_service_process = Process(target=run_mpc_service, args=(mpc_conn,))
    mpc_service_process.start()

    mqtt_service_process = Process(target=run_mqtt_service, args=([inst_queue],))
    mqtt_service_process.start()

    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        pass
    finally:
        mpc_service_process.join()  # 等待MPC服务进程结束
        state_service_process.join()
        mqtt_service_process.join()

        inst_queue.close()
        state_conn.close()
        mpc_conn.close()


if __name__ == "__main__":
    mpc_start()
