import logging
from typing import List, Optional

from sqlalchemy.orm import Session

from database.schemas import StripInfoBase, StripTrackingBase

from . import models

logger = logging.getLogger(__name__)

# ===================================================================
# CRUD apen for StripInfo
# ===================================================================


def create_strip_info(db: Session, strip_info_data: StripInfoBase) -> models.StripInfo:
    """
    创建一个新的 StripInfo 记录。
    :param db: 数据库会话。
    :param strip_info_data: 一个包含 StripInfo 字段的字典。
    :return: 新创建的 StripInfo ORM 对象。
    """
    # 使用字典解包来创建模型实例
    db_strip_info = models.StripInfo(**strip_info_data.model_dump())
    db.add(db_strip_info)
    # commit 和 refresh 通常在会话作用域的末尾执行，但这里为了立即获取ID，可以这样做
    # 在我们的单例模式下，commit在 with 块结束时发生
    # db.commit()
    # db.refresh(db_strip_info)
    return db_strip_info


def get_strip_info(db: Session, strip_info_id: int) -> Optional[models.StripInfo]:
    """
    根据主键 ID 获取单个 StripInfo 记录。
    :param db: 数据库会话。
    :param strip_info_id: 记录的主键 ID。
    :return: StripInfo ORM 对象或 None。
    """
    return db.query(models.StripInfo).filter(models.StripInfo.id == strip_info_id).first()


def get_strip_info_by_name(db: Session, strip_name: str) -> Optional[models.StripInfo]:
    """
    根据钢卷名称 (STRIP_NAME) 获取单个 StripInfo 记录。
    :param db: 数据库会话。
    :param strip_name: 钢卷的名称。
    :return: StripInfo ORM 对象或 None。
    """
    return db.query(models.StripInfo).filter(models.StripInfo.STRIP_NAME == strip_name).first()


def get_all_strip_infos(db: Session, skip: int = 0, limit: int = 100) -> List[models.StripInfo]:
    """
    获取 StripInfo 记录列表（支持分页）。
    :param db: 数据库会话。
    :param skip: 跳过的记录数。
    :param limit: 返回的最大记录数。
    :return: StripInfo ORM 对象的列表。
    """
    return db.query(models.StripInfo).offset(skip).limit(limit).all()


def update_strip_info(db: Session, strip_info_id: int, strip_info_update_data: StripInfoBase) -> Optional[models.StripInfo]:
    """
    更新一个已存在的 StripInfo 记录。
    :param db: 数据库会话。
    :param strip_info_id: 要更新记录的主键 ID。
    :param update_data: 包含要更新字段的字典。
    :return: 更新后的 StripInfo ORM 对象或 None。
    """
    db_strip_info = get_strip_info(db, strip_info_id)
    if db_strip_info:
        update_data = strip_info_update_data.model_dump()
        for key, value in update_data.items():
            # 确保只更新模型中存在的属性
            if hasattr(db_strip_info, key):
                setattr(db_strip_info, key, value)
        # 变更将在会话 commit 时保存
        return db_strip_info
    return None


def update_or_create_strip_info(db: Session, strip_info_data: StripInfoBase) -> models.StripInfo:
    """
    更新或创建 StripInfo 记录。
    :param db: 数据库会话。
    :param strip_info_data: 包含 StripInfo 数据的字典。
    :return: 被更新或创建的 StripInfo 对象。
    """
    db_strip_info = get_strip_info_by_name(db, strip_info_data.STRIP_NAME)
    if db_strip_info:
        update_dict = strip_info_data.model_dump()
        for key, value in update_dict.items():
            # 确保只更新模型中存在的属性
            if hasattr(db_strip_info, key):
                setattr(db_strip_info, key, value)
        return db_strip_info
    else:
        return create_strip_info(db, strip_info_data)


def delete_strip_info(db: Session, strip_info_id: int) -> bool:
    """
    删除一个 StripInfo 记录。
    :param db: 数据库会话。
    :param strip_info_id: 要删除记录的主键 ID。
    :return: 如果删除成功返回 True，否则返回 False。
    """
    db_strip_info = get_strip_info(db, strip_info_id)
    if db_strip_info:
        db.delete(db_strip_info)
        return True
    return False


# ===================================================================
# CRUD apen for StripTracking
# ===================================================================


# 定义一个常量来表示我们唯一的行的主键
STRIP_STATUS_ID = 1


def update_or_create_strip_status(db: Session, status_data: StripTrackingBase) -> models.StripTracking:
    """
    更新或创建唯一的 StripTracking 状态记录。
    这是此表最主要的写入方法。

    :param db: 数据库会话。
    :param status_data: 包含要更新或创建的状态数据的字典。
    :return: 被更新或创建的 StripTracking 对象。
    """
    # 尝试获取 id=1 的记录
    status_row = db.query(models.StripTracking).filter(models.StripTracking.id == STRIP_STATUS_ID).first()

    if status_row:
        # --- 如果记录存在，则更新 ---
        # logger.info(f"Updating existing strip status (ID: {STRIP_STATUS_ID})...")
        update_dict = status_data.model_dump()
        for key, value in update_dict.items():
            if hasattr(status_row, key):
                setattr(status_row, key, value)
    else:
        # --- 如果记录不存在，则创建 ---
        # logger.info(f"Creating new strip status (ID: {STRIP_STATUS_ID})...")
        # 确保新记录的 id 是我们固定的 ID
        status_row = models.StripTracking(id=STRIP_STATUS_ID, **status_data.model_dump())
        db.add(status_row)

    return status_row


def get_strip_status(db: Session) -> Optional[models.StripTracking]:
    """
    获取唯一的 StripTracking 状态记录。

    :param db: 数据库会话。
    :return: StripTracking 对象或 None (如果从未创建过)。
    """
    return db.query(models.StripTracking).filter(models.StripTracking.id == STRIP_STATUS_ID).first()


def delete_strip_status(db: Session) -> bool:
    """
    删除（或清空）当前的 StripTracking 状态。

    :param db: 数据库会话。
    :return: 如果成功删除返回 True，否则 False。
    """
    status_row = get_strip_status(db)
    if status_row:
        # logger.info(f"Deleting current strip status (ID: {STRIP_STATUS_ID})...")
        db.delete(status_row)
        return True
    print("No strip status to delete.")
    return False
