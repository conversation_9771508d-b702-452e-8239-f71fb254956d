# 构建说明

本项目使用 setuptools 和 Nuitka 将 Python 应用程序打包为独立的可执行文件。

## 文件说明

- `setup.py`: 基于 setuptools 的构建脚本，包含自定义 Nuitka 构建命令
- `pyproject.toml`: 项目配置文件，包含依赖和元数据
- `scripts/build_exe.bat`: 原始的批处理构建脚本（仅支持 MPC）

## 使用方法

### 1. 查看可用命令

```bash
python setup.py --help-commands
```

### 2. 构建可执行文件

构建两个可执行文件（共享依赖）：
```bash
python setup.py build_exe
```

### 3. 查看命令帮助

```bash
python setup.py build_exe --help
```

## 输出文件

构建完成后，两个可执行文件将位于同一目录并共享依赖：

- 训练器：`dist/afmpc/trainer.exe`
- MPC 控制器：`dist/afmpc/mpc.exe`
- 共享依赖库：`dist/afmpc/` 目录下的其他文件

**优势：** 两个可执行文件共享相同的依赖库，大大节省磁盘空间。

## 构建配置

### 基于 build_exe.bat 的配置

所有配置都基于原始的 `scripts/build_exe.bat` 文件，包括：

- **独立模式** (`--standalone`): 生成包含所有依赖的独立可执行文件
- **Windows 元数据**:
  - 公司名称: CISDI
  - 产品名称: AFMPC
  - 版本: *******
  - 描述: Model Predictive Control for Annealing Furnace
- **图标**: `./assets/icon.ico`
- **数据目录包含**:
  - `./config` → `./config`
  - `./db` → `./db`
  - `./logs` → `./logs`
- **插件配置**: 禁用 Qt 插件 (`--enable-plugin=no-qt`)
- **清理**: 自动删除构建临时文件 (`--remove-output`)

### 自定义配置

如需修改构建配置，请编辑 `setup.py` 文件中的 `NUITKA_OPTIONS` 字典。

## 依赖要求

确保已安装 Nuitka：

```bash
pip install nuitka
```

或者使用项目的依赖管理：

```bash
uv sync
```

## 故障排除

1. **构建失败**: 检查是否安装了所有必需的依赖
2. **缺少文件**: 确保 `assets/icon.ico` 文件存在
3. **权限问题**: 在 Windows 上可能需要管理员权限

## 与原始 build_exe.bat 的对比

| 特性         | build_exe.bat  | setup.py (setuptools)          |
| ------------ | -------------- | ------------------------------ |
| 支持的入口点 | 仅 main_mpc.py | main_trainer.py 和 main_mpc.py |
| 配置管理     | 硬编码在脚本中 | 基于 setuptools 的标准化配置   |
| 扩展性       | 有限           | 易于添加新的自定义命令         |
| 错误处理     | 基本           | 详细的错误信息和状态报告       |
| 跨平台       | 仅 Windows     | 可扩展到其他平台               |
| 集成性       | 独立脚本       | 与 Python 包管理生态系统集成   |
| 标准化       | 自定义方案     | 遵循 Python 打包标准           |
| 依赖共享     | 不支持         | 两个可执行文件共享依赖库       |
| 磁盘空间     | 较大           | 节省空间（共享依赖）           |

## 优势

1. **标准化**: 使用 setuptools 标准，与 Python 生态系统完全兼容
2. **空间节省**: 两个可执行文件共享依赖库，大幅减少磁盘占用
3. **简化部署**: 单一目录包含所有必需文件，便于分发和部署
4. **集成性**: 可以与其他 setuptools 命令（如 install、build 等）配合使用
5. **维护性**: 代码结构清晰，易于维护和扩展
6. **兼容性**: 保持与原始 build_exe.bat 完全相同的 Nuitka 参数
