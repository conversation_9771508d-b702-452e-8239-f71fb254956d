#!/usr/bin/env python3
"""
Setup script for building executables using setuptools and Nuitka
Based on the configuration from scripts/build_exe.bat
"""

import subprocess
import sys
from pathlib import Path

from setuptools import Command, find_packages, setup

# 基于 build_exe.bat 的 Nuitka 配置
NUITKA_OPTIONS = {
    "standalone": True,
    "windows_icon_from_ico": "./assets/icon.ico",
    "windows_company_name": "CISDI",
    "windows_product_name": "AFMPC",
    "windows_file_version": "*******",
    "windows_product_version": "*******",
    "windows_file_description": "Model Predictive Control for Annealing Furnace",
    "include_data_dirs": [
        ("./config", "./config"),
        ("./db", "./db"),
        ("./logs", "./logs"),
    ],
    "enable_plugins": [],
    "disable_plugins": ["qt"],
    "remove_output": True,
}


class NuitkaBuildCommand(Command):
    """Base class for Nuitka build commands"""

    def _build_executable(self, entry_point, output_name, output_dir, description=None):
        """Build executable using Nuitka"""

        # Ensure output directory exists
        Path(output_dir).mkdir(parents=True, exist_ok=True)

        # Build Nuitka command arguments
        cmd = [sys.executable, "-m", "nuitka"]

        # Add basic options
        if NUITKA_OPTIONS["standalone"]:
            cmd.append("--standalone")

        if NUITKA_OPTIONS["remove_output"]:
            cmd.append("--remove-output")

        # Add Windows metadata
        cmd.extend(
            [
                f"--windows-icon-from-ico={NUITKA_OPTIONS['windows_icon_from_ico']}",
                f"--windows-company-name={NUITKA_OPTIONS['windows_company_name']}",
                f"--windows-product-name={NUITKA_OPTIONS['windows_product_name']}",
                f"--windows-file-version={NUITKA_OPTIONS['windows_file_version']}",
                f"--windows-product-version={NUITKA_OPTIONS['windows_product_version']}",
                f"--windows-file-description={description or NUITKA_OPTIONS['windows_file_description']}",
            ]
        )

        # Add data directories
        for src_dir, dst_dir in NUITKA_OPTIONS["include_data_dirs"]:
            cmd.append(f"--include-data-dir={src_dir}={dst_dir}")

        # Add plugin options
        for plugin in NUITKA_OPTIONS["disable_plugins"]:
            cmd.append(f"--enable-plugin=no-{plugin}")

        # Add output options
        cmd.extend([f"--output-dir={output_dir}", "-o", output_name, entry_point])

        print(f"Building {entry_point} -> {output_name}")
        print(f"Output directory: {output_dir}")
        print("-" * 80)

        # Execute Nuitka build
        try:
            subprocess.run(cmd, check=True)
            print(f"✓ Successfully built {output_name}")
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to build {output_name}: {e}")
            raise


class BuildExecutablesCommand(NuitkaBuildCommand):
    """Custom command to build both executables with shared dependencies using Nuitka"""

    description = "Build both trainer and MPC executables with shared dependencies using Nuitka"
    user_options = []

    def initialize_options(self):
        pass

    def finalize_options(self):
        pass

    def run(self):
        print("Building both executables with shared dependencies...")
        print("=" * 80)

        # 共享的输出目录
        shared_output_dir = "./dist/afmpc"

        # 先构建 trainer (standalone模式，包含所有依赖)
        print("Step 1: Building trainer executable (with all dependencies)...")
        self._build_executable(
            entry_point="./src/main_trainer.py",
            output_name="trainer",
            output_dir=shared_output_dir,
            description="Model Training Application for Annealing Furnace",
        )

        print("\nStep 2: Building MPC executable (reusing dependencies)...")
        # 构建 MPC，直接放到同一个目录，这样可以共享依赖库
        self._build_executable(
            entry_point="./src/main_mpc.py",
            output_name="mpc",
            output_dir=shared_output_dir,
            description="Model Predictive Control for Annealing Furnace",
        )

        print("\n" + "=" * 80)
        print("✓ Both executables built successfully with shared dependencies!")
        print(f"Trainer executable: {shared_output_dir}/trainer.exe")
        print(f"MPC executable: {shared_output_dir}/mpc.exe")
        print(f"Shared dependencies location: {shared_output_dir}/")
        print("Note: Both executables share the same dependency libraries in the output directory.")


# 读取项目信息
def read_file(filename):
    """Read file content"""
    try:
        with open(filename, "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return ""


# Setup configuration
setup(
    name="model-predictive-control",
    version="1.0.0",
    description="Model Predictive Control for Annealing Furnace",
    long_description=read_file("README.md"),
    long_description_content_type="text/markdown",
    author="CISDI",
    packages=find_packages(),
    python_requires=">=3.12",
    install_requires=[
        "influxdb>=5.3.2",
        "lightning>=2.5.1",
        "matplotlib>=3.10.1",
        "nuitka>=2.7.12",
        "numpy>=2.2.4",
        "paho-mqtt>=2.1.0",
        "pandas>=2.2.3",
        "pandas-stubs>=2.3.0.250703",
        "pydantic>=2.11.7",
        "scikit-learn>=1.6.1",
        "seaborn>=0.13.2",
        "sqlalchemy>=2.0.42",
        "tensorboard>=2.19.0",
        "torch>=2.6.0",
        "torchvision>=0.21.0",
    ],
    entry_points={
        "console_scripts": [
            "afmpc-trainer=src.main_trainer:main",
            "afmpc-mpc=src.main_mpc:main",
        ],
    },
    cmdclass={
        "build_exe": BuildExecutablesCommand,
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Manufacturing",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.12",
        "Operating System :: Microsoft :: Windows",
    ],
)
