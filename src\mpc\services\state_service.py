import logging
import queue
from datetime import datetime, timezone
from multiprocessing import Queue
from multiprocessing.connection import Connection
from threading import Event, Thread
from typing import Any, Optional
from zoneinfo import ZoneInfo

import numpy as np
import pandas as pd

from config.configs import Configs
from config.logger import Logger
from consts import LogFileNameEnum as LFN
from database.database import get_db_manager, init_database
from mpc.schemas.message_schemas import FutureDataRequest, PastDataRequest, ResponseBase
from track import StripTracker
from track.track import Strip, TrackingMsg
from utils import timer
from utils.data_utils import downsample, fill_na_with_moving_average
from utils.influxql_helper import InfluxQuery
from utils.pd_utils import safe_to_numeric


class StateService:
    """
    系统状态管理服务
    """

    def __init__(self, input_queue: Queue, pipe_conn: Connection):
        Configs.initialize()
        Logger.initialize(file_name=LFN.MPC_STATE.value)

        self.logger = logging.getLogger(self.__class__.__name__)
        self.input_queue = input_queue
        self.pipe_conn = pipe_conn

        # 初始化数据库
        init_database(Configs.get_db_config())
        self.dbm = get_db_manager()
        self.dbm.create_tables()

        self.strip_tracker = StripTracker(online_mode=True, work_dir="")
        self.realtime_df = RealTimeDataFrame()

    def start(self):
        self.logger.info("状态服务启动中...")

        stop_event = Event()
        queue_thread = Thread(target=self.handle_queue, args=(stop_event,))
        pipe_thread = Thread(target=self.handle_pipe, args=(stop_event,))

        try:
            queue_thread.start()
            pipe_thread.start()

            # 主线程等待工作线程结束
            while queue_thread.is_alive() or pipe_thread.is_alive():
                queue_thread.join(timeout=1)
                pipe_thread.join(timeout=1)

        except KeyboardInterrupt:
            self.logger.warning("收到中断信号，正在停止服务...")
            stop_event.set()
        except Exception as ex:
            self.logger.error(f"状态服务异常: {ex}")
            stop_event.set()
        finally:
            # 确保设置停止事件
            stop_event.set()
            # 等待线程结束
            queue_thread.join(timeout=1.0)
            pipe_thread.join(timeout=1.0)

            self.logger.info("状态服务已停止.")

    def handle_queue(self, stop_event: Event):
        self.logger.info("队列处理线程启动")
        while not stop_event.is_set():
            try:
                record = self.input_queue.get(timeout=0.5)
                record_tracked = self._strip_track_online(record)

                if record_tracked is None:
                    self.logger.warning("跟踪失败...")
                    continue

                self.logger.info(f"跟踪结果: {record_tracked}")

                # TODO: 保存跟踪数据
                self.realtime_df.add_data(record_tracked)
                self.realtime_df.trim_data()
                # self.logger.info(f"实时数据: {self.realtime_df.get_dataframe()}")
                self.logger.info(f"实时数据：{len(self.realtime_df.get_dataframe())}")

                # TODO: 状态管理

            except queue.Empty:  # 处理队列空异常
                continue
            except Exception as ex:
                self.logger.error(f"消息处理失败：{ex}")

        self.logger.info("队列处理线程退出")

    def handle_pipe(self, stop_event: Event):
        self.logger.info("通讯处理线程启动")
        while not stop_event.is_set():
            try:
                if self.pipe_conn.poll(1):
                    request: PastDataRequest | FutureDataRequest = self.pipe_conn.recv()

                    # TODO: 通讯处理

                    match request:
                        case PastDataRequest():
                            past_df = self.realtime_df.get_dataframe_processed()
                            response = ResponseBase(success=True, message="历史数据请求成功", data=past_df)
                        case FutureDataRequest():
                            future_df = self.strip_tracker.get_future_data()
                            response = ResponseBase(success=True, message="预测数据请求成功", data=future_df)
                        case _:
                            self.logger.warning(f"未知请求类型: {type(request)}")
                            response = ResponseBase(success=False, message="未知请求类型")

                    self.pipe_conn.send(response)
            except EOFError:
                self.logger.warning("管道连接已关闭")
                stop_event.set()
                break
            except Exception as ex:
                self.logger.error(f"消息处理失败：{ex}")

        self.logger.info("通讯处理线程退出")

    @timer
    def _strip_track_online(self, record):
        """
        钢卷在线跟踪
        """
        timestamp = record.get("TIME_STAMP")
        selected = record.get("SELECTED")
        strip1 = Strip(
            name=record.get("STRIP_NAME_1"),
            type=record.get("STRIP_TYPE_1"),
            width=record.get("STRIP_WIDTH_1"),
            thick=record.get("STRIP_THICK_1"),
            length=record.get("STRIP_LENGTH_1"),
            weight=record.get("STRIP_WEIGHT_1"),
        )
        strip2 = Strip(
            name=record.get("STRIP_NAME_2"),
            type=record.get("STRIP_TYPE_2"),
            width=record.get("STRIP_WIDTH_2"),
            thick=record.get("STRIP_THICK_2"),
            length=record.get("STRIP_LENGTH_2"),
            weight=record.get("STRIP_WEIGHT_2"),
        )
        track_msg = TrackingMsg(
            timestamp=timestamp,
            selected=selected,
            strip1=strip1,
            strip2=strip2,
            weld1=record.get("WELD_POSITION_1"),
            weld2=record.get("WELD_POSITION_2"),
            speed=record.get("SPEED"),
        )
        self.strip_tracker.update(track_msg)

        heating_strip: Optional[Strip] = self.strip_tracker.get_heating_strip()

        if heating_strip is None:
            return None

        record["STRIP_NAME"] = heating_strip.name
        record["STRIP_TYPE"] = heating_strip.type
        record["STRIP_WIDTH"] = heating_strip.width
        record["STRIP_THICK"] = heating_strip.thick
        record["STRIP_LENGTH"] = heating_strip.length
        record["STRIP_WEIGHT"] = heating_strip.weight
        record["WELD"] = heating_strip.weld2

        return record


class RealTimeDataFrame:
    def __init__(self):
        self.train_configs = Configs.get_train_config()

        self.logger = logging.getLogger(self.__class__.__name__)
        self.index = self.train_configs.index_column
        self.columns = self.train_configs.input_columns
        self.time_window = pd.to_timedelta(self.train_configs.freq) * self.train_configs.input_len  # 时间窗口

        # 初始化一个空的DataFrame，并将索引设置为datetime类型
        # 这是最高效写法的关键：使用时间序列作为索引
        # 注意：只有数据列作为DataFrame的列，索引列单独设置
        self.df = pd.DataFrame(columns=self.columns)
        self.df.index = pd.DatetimeIndex([], name=self.index)  # 设置为DatetimeIndex类型
        self.df = self.df.astype({col: float for col in self.columns})  # 预设数据类型以提高效率

        self.init_df()

    @timer
    def init_df(self):
        """
        初始化DataFrame
        """
        influx_config = Configs.get_influx_db_config()
        influx_query = InfluxQuery(
            host=influx_config.host,
            port=influx_config.port,
            username=influx_config.username,
            password=influx_config.password,
            database=influx_config.database,
        )

        utc8_tz = ZoneInfo("Asia/Shanghai")

        try:
            end_time = datetime.now().replace(tzinfo=utc8_tz)
            # end_time = datetime.strptime("2025-05-01 09:00:00", "%Y-%m-%d %H:%M:%S").replace(tzinfo=utc8_tz)
            start_time = (end_time - self.time_window * 5).replace(tzinfo=utc8_tz)  # 初始化5个时间窗口的数据

            df = influx_query.batch_query_aligned_df(
                measurements=list(self.train_configs.measurements.keys()),
                field="value",
                start_time=start_time.astimezone(timezone.utc),
                end_time=end_time.astimezone(timezone.utc),
                time_interval="1s",
                fill_method="ffill",
                chunk_size="1d",
            )

            if not isinstance(df, pd.DataFrame):
                self.logger.error(f"查询结果类型错误: {type(df)}")
                return

            if df is None or df.empty:
                self.logger.warning("初始化DataFrame，查询结果为空")
                return

            df = df.rename(columns=self.train_configs.measurements)
            df.index.name = self.index
            df.reset_index(inplace=True)

            # 离线跟踪
            strip_tracker = StripTracker(online_mode=False, work_dir="")
            tracking = {col: [] for col in self.train_configs.track_columns}
            for row in df.itertuples(index=False, name="DataRow"):
                timestamp = datetime.fromisoformat(str(getattr(row, self.index))).astimezone(utc8_tz)
                selected = bool(row.SELECTED)
                strip1 = Strip(
                    name=str(row.STRIP_NAME_1).strip(),
                    type=str(row.STRIP_TYPE_1).strip(),
                    width=safe_to_numeric(row.STRIP_WIDTH_1),
                    thick=safe_to_numeric(row.STRIP_THICK_1),
                    length=safe_to_numeric(row.STRIP_LENGTH_1),
                    weight=safe_to_numeric(row.STRIP_WEIGHT_1),
                )
                strip2 = Strip(
                    name=str(row.STRIP_NAME_2).strip(),
                    type=str(row.STRIP_TYPE_2).strip(),
                    width=safe_to_numeric(row.STRIP_WIDTH_2),
                    thick=safe_to_numeric(row.STRIP_THICK_2),
                    length=safe_to_numeric(row.STRIP_LENGTH_2),
                    weight=safe_to_numeric(row.STRIP_WEIGHT_2),
                )
                track_msg = TrackingMsg(
                    timestamp=timestamp,
                    selected=selected,
                    strip1=strip1,
                    strip2=strip2,
                    weld1=safe_to_numeric(row.WELD_POSITION_1, default=0),
                    weld2=safe_to_numeric(row.WELD_POSITION_2, default=0),
                    speed=safe_to_numeric(row.SPEED, default=0),
                )

                strip_tracker.update(track_msg)
                heating_strip: Optional[Strip] = strip_tracker.get_heating_strip()

                if heating_strip is not None:
                    tracking["STRIP_NAME"].append(heating_strip.name)
                    tracking["STRIP_TYPE"].append(heating_strip.type)
                    tracking["STRIP_WIDTH"].append(heating_strip.width)
                    tracking["STRIP_THICK"].append(heating_strip.thick)
                    tracking["STRIP_LENGTH"].append(heating_strip.length)
                    tracking["STRIP_WEIGHT"].append(heating_strip.weight)
                    tracking["WELD"].append(heating_strip.weld2)
                else:
                    # 确保结果长度对齐
                    for col in self.train_configs.track_columns:
                        tracking[col].append(None)

            for col in self.train_configs.track_columns:
                df[col] = tracking[col]

            # 重新设置索引并合并
            df = df.set_index(self.index)
            df.index = df.index.tz_convert(utc8_tz)  # type: ignore
            df.index = df.index.tz_localize(None)  # type: ignore
            self.df = pd.concat([self.df, df], join="inner")
            self.df.sort_index(inplace=True)  # 确保合并后的索引是排序的
            self.trim_data()

            self.logger.info(self.df.head())

        except Exception as ex:
            self.logger.error(f"初始化DataFrame失败: {ex}")
        finally:
            influx_query.close()

    @timer
    def add_data(self, record: dict[str, Any]):
        """
        向DataFrame中添加新的数据行。

        Args:
            record (dict): 包含新数据的字典，必须有一个'TIMESTAMP'键，
                              其值为datetime对象。
        """
        # 1. 索引准备
        # ----------------------------------------------------
        timestamp = record.pop(self.index, None)
        if timestamp is None:
            self.logger.warning(f"警告：传入的数据字典缺少 '{self.index}' 键，该数据将被忽略。")
            return

        if not isinstance(timestamp, datetime):
            raise TypeError("TIMESTAMP 的值必须是 datetime 对象。")

        # 2. 按照 self.df.columns 的顺序，从 data_dict 中构建一个值的列表。
        #    使用 .get() 方法来安全地获取值，如果字典中缺少某个键，则使用 np.nan 作为默认值。
        #    注意：只使用数据列，索引列已经通过timestamp参数处理
        row_values = [record.get(col, np.nan) for col in self.df.columns]

        # 3. 使用高效的 .loc 赋值方式
        #    直接使用 .loc 进行赋值，这是最高效的方式
        self.df.loc[timestamp, :] = row_values
        # --- 修正结束 ---

        # 确保索引是排序的，这对时间序列操作性能至关重要
        # 如果进入的时间戳保证是单调递增的，可以省略此步以获得更高性能
        self.df.sort_index(inplace=True)

    @timer
    def trim_data(self):
        """
        根据时间窗口，从DataFrame中移除旧的数据。
        """
        cutoff_time = datetime.now() - self.time_window
        self.df = self.df[self.df.index >= cutoff_time]

    def get_dataframe(self) -> pd.DataFrame:
        """返回当前的DataFrame副本"""
        return self.df.copy()

    def get_dataframe_processed(self) -> pd.DataFrame:
        """返回预处理后的DataFrame副本"""
        df = self.df.copy()

        # 缺失值处理
        df = fill_na_with_moving_average(df)
        # 重采样
        df = downsample(df, freq=self.train_configs.freq)
        # 删除依旧为NAN的行
        df = df.dropna()

        return df
