import json
import logging
import signal
import threading
import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from queue import Empty, Queue
from typing import Any

import paho.mqtt.client as mqtt
import pandas as pd

from config import Configs, InfluxDBCofing, MQTTConfig
from utils import influxql_helper


@dataclass
class SimulatorConfig:
    """模拟器配置类"""

    send_interval: float = 1.0  # 发送间隔（秒）
    buffer_size: int = 5000  # 缓冲区大小
    low_buffer_threshold: int = 120  # 低缓冲区阈值，触发数据加载


class MQTTDataSimulator:
    """MQTT 数据模拟器

    功能特性：
    1. 从 InfluxDB 异步加载历史数据
    2. 按指定间隔向 MQTT 主题发送数据
    3. 数据缓冲区管理，确保连续发送
    4. 优雅的资源管理和关闭机制
    5. 完整的错误处理和日志记录
    """

    def __init__(
        self,
        influx_config: InfluxDBCofing,
        mqtt_config: MQTTConfig,
        simulator_config: SimulatorConfig,
        start_time: datetime,
        end_time: datetime | None = None,
    ):
        """初始化 MQTT 数据模拟器

        Args:
            influx_config: InfluxDB 连接配置
            mqtt_config: MQTT 连接配置
            simulator_config: 模拟器配置
            start_time: 数据开始时间
            end_time: 数据结束时间，如果为 None 则持续循环发送
        """
        self.influx_config = influx_config
        self.mqtt_config = mqtt_config
        self.simulator_config = simulator_config
        self.start_time = start_time
        self.end_time = end_time

        self.measurements = Configs.get_train_config().measurements.copy()

        # 初始化日志
        self.logger = logging.getLogger(self.__class__.__name__)

        # 数据缓冲区
        self.data_buffer = Queue(maxsize=simulator_config.buffer_size)

        # 控制标志
        self._running = False
        self._stop_event = threading.Event()

        # 线程管理
        self._data_loader_thread = None
        self._mqtt_sender_thread = None

        # InfluxDB 和 MQTT 客户端
        self._influx_client = None
        self._mqtt_client = None

        # 数据加载状态
        self._current_load_time = start_time
        self._data_loading = False
        self._data_exhausted = False

        # 统计信息
        self._total_sent = 0
        self._total_loaded = 0
        self._start_timestamp = None

        # 注册信号处理器
        self._setup_signal_handlers()

    def _setup_signal_handlers(self):
        """设置信号处理器，用于优雅关闭"""

        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，开始关闭...")
            self.stop()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def _get_influx_client(self) -> influxql_helper.InfluxQuery:
        """获取 InfluxDB 客户端"""
        if self._influx_client is None:
            self._influx_client = influxql_helper.InfluxQuery(
                self.influx_config.host,
                self.influx_config.port,
                self.influx_config.username,
                self.influx_config.password,
                self.influx_config.database,
            )
        return self._influx_client

    def _get_mqtt_client(self) -> mqtt.Client:
        """获取 MQTT 客户端"""
        if self._mqtt_client is None:
            self._mqtt_client = mqtt.Client()

            # 设置认证信息
            if self.mqtt_config.user_name and self.mqtt_config.password:
                self._mqtt_client.username_pw_set(self.mqtt_config.user_name, self.mqtt_config.password)

            # 设置回调函数
            self._mqtt_client.on_connect = self._on_mqtt_connect
            self._mqtt_client.on_disconnect = self._on_mqtt_disconnect
            self._mqtt_client.on_publish = self._on_mqtt_publish

            # 连接到 MQTT broker
            try:
                self._mqtt_client.connect(self.mqtt_config.broker, self.mqtt_config.port, 60)
                self._mqtt_client.loop_start()
                self.logger.info(f"MQTT 客户端已连接到 {self.mqtt_config.broker}:{self.mqtt_config.port}")
            except Exception as e:
                self.logger.error(f"MQTT 连接失败: {e}")
                raise

        return self._mqtt_client

    def _on_mqtt_connect(self, client, userdata, flags, rc):
        """MQTT 连接回调"""
        if rc == 0:
            self.logger.info("MQTT 连接成功")
        else:
            self.logger.error(f"MQTT 连接失败，返回码: {rc}")

    def _on_mqtt_disconnect(self, client, userdata, rc):
        """MQTT 断开连接回调"""
        if rc != 0:
            self.logger.warning(f"MQTT 意外断开连接，返回码: {rc}")
        else:
            self.logger.info("MQTT 正常断开连接")

    def _on_mqtt_publish(self, client, userdata, mid):
        """MQTT 发布回调"""
        self.logger.debug(f"消息 {mid} 发布成功")

    def _load_data_batch(self) -> pd.DataFrame | None:
        """加载一批数据

        Returns:
            加载的数据 DataFrame，如果没有更多数据则返回 None
        """
        if self._data_exhausted:
            return None

        try:
            # 计算当前批次的结束时间
            batch_end_time = self._current_load_time + timedelta(hours=1)

            # 如果设置了结束时间，确保不超过结束时间
            if self.end_time and batch_end_time > self.end_time:
                batch_end_time = self.end_time

            self.logger.info(
                f"加载数据批次: {self._current_load_time.strftime('%Y-%m-%d %H:%M:%S')} 到 {batch_end_time.strftime('%Y-%m-%d %H:%M:%S')}"
            )

            # 使用 InfluxDB 客户端查询数据
            influx_client = self._get_influx_client()
            df = influx_client.batch_query_aligned_df(
                measurements=list(self.measurements.keys()),
                field="value",
                start_time=self._current_load_time,
                end_time=batch_end_time,
                resample=True,
                time_interval="1s",  # 重采样到1秒间隔
                fill_method="ffill",
            )

            if df is not None and not df.empty:
                # 确保返回的是 DataFrame 类型
                if isinstance(df, pd.Series):
                    df = df.to_frame()

                self.logger.info(f"成功加载 {len(df)} 行数据")
                self._total_loaded += len(df)

                # 更新当前加载时间
                self._current_load_time = batch_end_time

                # 检查是否到达结束时间
                if self.end_time and self._current_load_time >= self.end_time:
                    self._data_exhausted = True
                    self.logger.info("已到达数据结束时间")

                return df
            else:
                self.logger.warning("查询结果为空，尝试跳过当前时间段")
                self._current_load_time = batch_end_time

                # 如果连续多次查询为空，可能数据已耗尽
                if self.end_time and self._current_load_time >= self.end_time:
                    self._data_exhausted = True
                    self.logger.info("数据已耗尽")

                return None

        except Exception as e:
            self.logger.error(f"加载数据批次失败: {e}")
            # 即使失败也要推进时间，避免无限循环
            self._current_load_time += timedelta(hours=1)
            return None

    def _to_record(self, data: pd.Series) -> dict[str, Any] | None:
        metrics: list = []
        try:
            for col in data.index:
                metrics.append(self._generate_fields(col, data[col]))
            return {"metrics": metrics}
        except Exception as ex:
            self.logger.error(f"转换数据失败: {ex}")
            return None

    def _generate_fields(self, field: str, value: Any) -> dict[str, Any]:
        """生成数据记录"""
        return {
            "fields": {field: value},
            "name": "annealing_furnace",
            "tags": {"host": "localhost"},
            "timestamp": datetime.now().timestamp(),
        }

    def _data_loader_worker(self):
        """数据加载工作线程"""
        self.logger.info("数据加载线程启动")

        while self._running and not self._stop_event.is_set():
            try:
                # 检查缓冲区是否需要补充数据
                current_buffer_size = self.data_buffer.qsize()

                if current_buffer_size < self.simulator_config.low_buffer_threshold and not self._data_loading and not self._data_exhausted:
                    self._data_loading = True
                    self.logger.info(f"缓冲区数据不足 ({current_buffer_size})，开始加载新数据")

                    # 加载数据批次
                    df = self._load_data_batch()

                    if df is not None and not df.empty:
                        # rename columns
                        df = df.rename(columns=self.measurements)

                        # 将数据逐行添加到缓冲区
                        added_count = 0
                        for _, row in df.iterrows():
                            if self._stop_event.is_set():
                                break
                            try:
                                # 添加到缓冲区（非阻塞）
                                record = self._to_record(row)
                                if record:
                                    self.data_buffer.put(record, timeout=1.0)
                                    added_count += 1
                            except Exception as e:
                                if "Full" in str(e):
                                    self.logger.warning("缓冲区已满，停止添加数据")
                                    break
                                else:
                                    self.logger.error(f"添加数据到缓冲区失败: {e}")

                        self.logger.info(f"成功添加 {added_count} 条记录到缓冲区")

                    self._data_loading = False

                # 如果数据已耗尽且缓冲区为空
                elif self._data_exhausted and current_buffer_size == 0:
                    self.logger.info("数据发送完成，数据加载线程准备退出")
                    break

                # 短暂休眠，避免过度占用 CPU
                time.sleep(1.0)

            except Exception as e:
                self.logger.error(f"数据加载线程异常: {e}")
                time.sleep(5.0)  # 异常时等待更长时间

        self.logger.info("数据加载线程退出")

    def _mqtt_sender_worker(self):
        """MQTT 发送工作线程"""
        self.logger.info("MQTT 发送线程启动")

        while self._running and not self._stop_event.is_set():
            try:
                # 从缓冲区获取数据
                try:
                    record = self.data_buffer.get(timeout=1.0)
                except Empty:
                    # 缓冲区为空，检查是否应该继续等待
                    if self._data_exhausted and self.data_buffer.qsize() == 0:
                        self.logger.info("数据发送完成，MQTT 发送线程准备退出")
                        break
                    continue

                # 发送数据到 MQTT
                self._send_mqtt_message(record)

                # 更新统计信息
                self._total_sent += 1

                # 定期输出统计信息
                if self._total_sent % 100 == 0:
                    buffer_size = self.data_buffer.qsize()
                    elapsed_time = time.time() - self._start_timestamp if self._start_timestamp else 0
                    rate = self._total_sent / elapsed_time if elapsed_time > 0 else 0

                    self.logger.info(f"发送统计: 已发送 {self._total_sent} 条, 缓冲区 {buffer_size} 条, 发送速率 {rate:.2f} 条/秒")

                # 标记任务完成
                self.data_buffer.task_done()

                # 按指定间隔发送
                time.sleep(self.simulator_config.send_interval)

            except Exception as e:
                self.logger.error(f"MQTT 发送线程异常: {e}")
                time.sleep(1.0)

        self.logger.info("MQTT 发送线程退出")

    def _reset_timestamp(self, data_record: dict[str, Any], timestamp: float) -> dict[str, Any]:
        """重置数据记录的时间戳"""
        try:
            for metric in data_record["metrics"]:
                metric["timestamp"] = timestamp

            return data_record
        except Exception as e:
            self.logger.error(f"重置时间戳失败: {e}")
            return data_record

    def _send_mqtt_message(self, data_record: dict[str, Any]):
        """发送单条消息到 MQTT

        Args:
            data_record: 包含 timestamp 和 data 的数据记录
        """
        try:
            # 准备 MQTT 消息
            mqtt_message = self._reset_timestamp(data_record, datetime.now().timestamp())

            # 转换为 JSON 字符串
            message_json = json.dumps(mqtt_message, ensure_ascii=False, default=str)

            # 发送到 MQTT
            mqtt_client = self._get_mqtt_client()
            result = mqtt_client.publish(
                topic=self.mqtt_config.topic,
                payload=message_json,
                qos=0,  # QoS 0 (最多一次) QoS 1 (至少一次) QoS 2 (恰好一次)
                retain=False,
            )

            if result.rc != mqtt.MQTT_ERR_SUCCESS:
                self.logger.warning(f"MQTT 发布失败，返回码: {result.rc}")
            else:
                self.logger.debug(f"成功发送数据: {message_json}")

        except Exception as e:
            self.logger.error(f"发送 MQTT 消息失败: {e}")

    def start(self):
        """启动模拟器"""
        if self._running:
            self.logger.warning("模拟器已在运行中")
            return

        self.logger.info("启动 MQTT 数据模拟器")
        self._running = True
        self._start_timestamp = time.time()

        try:
            # 初始化 MQTT 客户端
            self._get_mqtt_client()

            # 启动数据加载线程
            self._data_loader_thread = threading.Thread(target=self._data_loader_worker, name="DataLoader", daemon=True)
            self._data_loader_thread.start()

            # 启动 MQTT 发送线程
            self._mqtt_sender_thread = threading.Thread(target=self._mqtt_sender_worker, name="MQTTSender", daemon=True)
            self._mqtt_sender_thread.start()

            self.logger.info("模拟器启动成功")

        except Exception as e:
            self.logger.error(f"启动模拟器失败: {e}")
            self.stop()
            raise

    def stop(self):
        """停止模拟器"""
        if not self._running:
            return

        self.logger.info("停止 MQTT 数据模拟器")
        self._running = False
        self._stop_event.set()

        # 等待线程结束
        if self._data_loader_thread and self._data_loader_thread.is_alive():
            self.logger.info("等待数据加载线程结束...")
            self._data_loader_thread.join(timeout=10.0)
            if self._data_loader_thread.is_alive():
                self.logger.warning("数据加载线程未能在超时时间内结束")

        if self._mqtt_sender_thread and self._mqtt_sender_thread.is_alive():
            self.logger.info("等待 MQTT 发送线程结束...")
            self._mqtt_sender_thread.join(timeout=10.0)
            if self._mqtt_sender_thread.is_alive():
                self.logger.warning("MQTT 发送线程未能在超时时间内结束")

        # 关闭资源
        self._cleanup_resources()

        # 输出最终统计信息
        elapsed_time = time.time() - self._start_timestamp if self._start_timestamp else 0
        self.logger.info(f"模拟器已停止 - 总发送: {self._total_sent} 条, 总加载: {self._total_loaded} 条, 运行时间: {elapsed_time:.2f} 秒")

    def _cleanup_resources(self):
        """清理资源"""
        try:
            # 关闭 InfluxDB 连接
            if self._influx_client:
                self._influx_client.close()
                self._influx_client = None
                self.logger.info("InfluxDB 连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭 InfluxDB 连接失败: {e}")

        try:
            # 关闭 MQTT 连接
            if self._mqtt_client:
                self._mqtt_client.loop_stop()
                self._mqtt_client.disconnect()
                self._mqtt_client = None
                self.logger.info("MQTT 连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭 MQTT 连接失败: {e}")

        # 清空缓冲区
        try:
            while not self.data_buffer.empty():
                self.data_buffer.get_nowait()
                self.data_buffer.task_done()
        except Exception as exc:
            self.logger.error(f"清空缓冲区失败: {exc}")

    def wait_for_completion(self):
        """等待模拟器完成（仅在非循环模式下有效）"""
        if self.end_time is None:
            self.logger.warning("循环模式下无法等待完成，请使用 stop() 方法停止")
            return

        try:
            # 等待数据加载线程完成
            if self._data_loader_thread:
                self._data_loader_thread.join()

            # 等待 MQTT 发送线程完成
            if self._mqtt_sender_thread:
                self._mqtt_sender_thread.join()

            self.logger.info("模拟器已完成所有数据发送")

        except KeyboardInterrupt:
            self.logger.info("接收到中断信号，停止模拟器")
            self.stop()

    def get_status(self) -> dict[str, Any]:
        """获取模拟器状态信息"""
        elapsed_time = time.time() - self._start_timestamp if self._start_timestamp else 0

        return {
            "running": self._running,
            "total_sent": self._total_sent,
            "total_loaded": self._total_loaded,
            "buffer_size": self.data_buffer.qsize(),
            "data_loading": self._data_loading,
            "data_exhausted": self._data_exhausted,
            "current_load_time": (self._current_load_time.isoformat() if self._current_load_time else None),
            "elapsed_time": elapsed_time,
            "send_rate": self._total_sent / elapsed_time if elapsed_time > 0 else 0,
        }

    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()
        # 返回 False 表示不抑制异常
        return False


if __name__ == "__main__":
    """使用示例"""
    import sys

    from config.logger import Logger

    Configs.initialize()
    Logger.initialize()
    logger = logging.getLogger("MQTTSimulatorExample")

    try:
        # 设置时间范围（示例：发送一小时的历史数据）
        start_time = datetime(2025, 4, 16, 0, 0, 0)
        end_time = datetime(2025, 4, 16, 1, 0, 0)

        logger.info("创建 MQTT 数据模拟器...")

        # 创建自定义配置
        simulator_config = SimulatorConfig(
            send_interval=1.0,  # 每秒发送一条数据
            buffer_size=2000,
            low_buffer_threshold=500,
        )

        # 初始化配置
        influx_config = Configs.get_influx_db_config()
        mqtt_config = Configs.get_mqtt_config()

        # 创建模拟器实例
        simulator = MQTTDataSimulator(
            influx_config=influx_config,
            mqtt_config=mqtt_config,
            simulator_config=simulator_config,
            start_time=start_time,
            end_time=end_time,
        )

        logger.info("启动模拟器...")

        # 使用上下文管理器确保资源正确释放
        with simulator:
            logger.info("模拟器已启动，开始发送数据...")

            # 定期输出状态信息
            while simulator._running:
                time.sleep(10)  # 每10秒输出一次状态
                status = simulator.get_status()
                logger.info(f"状态: 已发送 {status['total_sent']} 条, 缓冲区 {status['buffer_size']} 条, 发送速率 {status['send_rate']:.2f} 条/秒")

                # 检查是否完成
                if not status["running"] or (status["data_exhausted"] and status["buffer_size"] == 0):
                    logger.info("数据发送完成")
                    break

        logger.info("模拟器示例运行完成")

    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在停止...")
    except Exception as e:
        logger.error(f"运行模拟器时发生错误: {e}")
        sys.exit(1)
